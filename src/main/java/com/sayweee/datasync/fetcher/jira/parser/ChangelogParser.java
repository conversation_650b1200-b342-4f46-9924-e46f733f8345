package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ChangelogParser {
    public List<ChangeLogEntity> parse(Issue issue) {
        issue.getChangelog().forEach(changeLog -> {

        });
        return null;
    }
}
