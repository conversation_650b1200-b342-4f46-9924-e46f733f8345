package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.ChangelogGroup;
import com.atlassian.jira.rest.client.api.domain.ChangelogItem;
import com.atlassian.jira.rest.client.api.domain.Issue;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ChangelogParser {

    public List<ChangeLogEntity> parse(Issue issue) {
        List<ChangeLogEntity> changeLogEntities = new ArrayList<>();

        if (issue.getChangelog() == null) {
            return changeLogEntities;
        }

        for (ChangelogGroup changelogGroup : issue.getChangelog()) {
            try {
                List<ChangeLogEntity> groupEntities = parseChangelogGroup(changelogGroup, issue);
                changeLogEntities.addAll(groupEntities);
            } catch (Exception e) {
                log.warn("Failed to parse changelog group for issue {}: {}",
                    issue.getKey(), e.getMessage(), e);
            }
        }

        return changeLogEntities;
    }

    /**
     * 解析单个 ChangelogGroup
     */
    private List<ChangeLogEntity> parseChangelogGroup(ChangelogGroup changelogGroup, Issue issue) {
        List<ChangeLogEntity> entities = new ArrayList<>();

        if (changelogGroup.getItems() == null) {
            return entities;
        }

        for (ChangelogItem item : changelogGroup.getItems()) {
            try {
                ChangeLogEntity entity = parseChangelogItem(item, changelogGroup, issue);
                entities.add(entity);
            } catch (Exception e) {
                log.warn("Failed to parse changelog item {} for issue {}: {}",
                    item.getField(), issue.getKey(), e.getMessage(), e);
            }
        }

        return entities;
    }

    /**
     * 解析单个 ChangelogItem
     */
    private ChangeLogEntity parseChangelogItem(ChangelogItem item, ChangelogGroup group, Issue issue) {
        ChangeLogEntity entity = new ChangeLogEntity();

        // 设置变更ID (生成唯一ID)
        entity.setChangeId(generateChangeId(group, issue, item));

        // 设置 Issue Key
        entity.setKey(issue.getKey());

        // 设置项目ID
        if (issue.getProject() != null && issue.getProject().getId() != null) {
            entity.setProjectId(issue.getProject().getId().intValue());
        }

        // 设置用户信息
        if (group.getAuthor() != null) {
            entity.setUserId(group.getAuthor().getAccountId());
            entity.setUsername(group.getAuthor().getDisplayName());
        }

        // 设置变更时间
        entity.setCreated(convertDateTime(group.getCreated()));

        // 设置字段信息
        entity.setField(item.getField());
        entity.setFromValue(item.getFrom());
        entity.setFromString(item.getFromString());
        entity.setToValue(item.getTo());
        entity.setToString(item.getToString());

        // 设置入库时间
        entity.setInDate(LocalDateTime.now());

        return entity;
    }

    /**
     * 生成变更ID
     * 使用 created 时间戳 + issue key + field + author 的组合哈希值
     */
    private Integer generateChangeId(ChangelogGroup group, Issue issue, ChangelogItem item) {
        StringBuilder sb = new StringBuilder();

        // 添加时间戳
        if (group.getCreated() != null) {
            sb.append(group.getCreated().getMillis());
        }

        // 添加 issue key
        if (issue.getKey() != null) {
            sb.append(issue.getKey());
        }

        // 添加字段名
        if (item.getField() != null) {
            sb.append(item.getField());
        }

        // 添加作者
        if (group.getAuthor() != null && group.getAuthor().getAccountId() != null) {
            sb.append(group.getAuthor().getAccountId());
        }

        // 生成哈希值并转换为正整数
        return Math.abs(sb.toString().hashCode());
    }

    /**
     * 转换 Jira DateTime 到 OffsetDateTime
     */
    private OffsetDateTime convertDateTime(DateTime dateTime) {
        return dateTime == null ? null :
            OffsetDateTime.ofInstant(Instant.ofEpochMilli(dateTime.getMillis()), ZoneOffset.UTC);
    }
}
