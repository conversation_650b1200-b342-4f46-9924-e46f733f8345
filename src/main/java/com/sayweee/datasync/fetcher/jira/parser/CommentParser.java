package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.sayweee.datasync.model.entity.CommentEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CommentParser {
    public List<CommentEntity> parse(Issue issue) {

        for(var comment:issue.getComments()){
            log.info("comment: {}", comment);
            comment.g
        }

        return null;
    }
}
