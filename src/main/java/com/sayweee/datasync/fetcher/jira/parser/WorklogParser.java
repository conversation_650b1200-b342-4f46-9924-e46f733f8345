package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.sayweee.datasync.model.entity.CommentEntity;
import com.sayweee.datasync.model.entity.WorklogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class WorklogParser {
    public List<WorklogEntity> parse(Issue issue) {
        return null;
    }
}
